<?php
/**
 * Debug script for Wishlist functionality
 * 
 * This script helps identify issues with the wishlist functionality.
 * Access via: yoursite.com/wp-content/plugins/woo-custom/debug-wishlist.php?debug=1
 */

// Load WordPress
$wp_load_path = '../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('WordPress not found. Please check the path.');
}

// Only allow admin access
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

// Only run if debug parameter is set
if (!isset($_GET['debug'])) {
    die('Add ?debug=1 to the URL to run this debug script.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Woo Custom Wishlist Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
        .button { background: #007cba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>Woo Custom Wishlist Debug Report</h1>
    <p>Generated on: <?php echo date('Y-m-d H:i:s'); ?></p>

    <?php
    // Test 1: Plugin Status
    echo '<div class="test">';
    echo '<h2>1. Plugin Status</h2>';
    
    if (class_exists('WooCustom')) {
        echo '<div class="success">✓ Main plugin class loaded</div>';
    } else {
        echo '<div class="error">✗ Main plugin class not loaded</div>';
    }
    
    if (class_exists('WooCustom_Wishlist')) {
        echo '<div class="success">✓ Wishlist class loaded</div>';
    } else {
        echo '<div class="error">✗ Wishlist class not loaded</div>';
    }
    
    if (class_exists('WooCustom_Ajax')) {
        echo '<div class="success">✓ AJAX class loaded</div>';
    } else {
        echo '<div class="error">✗ AJAX class not loaded</div>';
    }
    echo '</div>';

    // Test 2: Database Table
    echo '<div class="test">';
    echo '<h2>2. Database Table</h2>';
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'woo_custom_wishlist';
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    
    if ($table_exists) {
        echo '<div class="success">✓ Wishlist table exists</div>';
        
        // Check table structure
        $columns = $wpdb->get_results("DESCRIBE $table_name");
        echo '<div class="info"><strong>Table Structure:</strong><pre>';
        foreach ($columns as $column) {
            echo $column->Field . ' (' . $column->Type . ')' . "\n";
        }
        echo '</pre></div>';
        
        // Check table data
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        echo '<div class="info">Total wishlist items: ' . $count . '</div>';
        
    } else {
        echo '<div class="error">✗ Wishlist table does not exist</div>';
        echo '<div class="warning">Try deactivating and reactivating the plugin to create the table.</div>';
    }
    echo '</div>';

    // Test 3: WooCommerce Integration
    echo '<div class="test">';
    echo '<h2>3. WooCommerce Integration</h2>';
    
    if (class_exists('WooCommerce')) {
        echo '<div class="success">✓ WooCommerce is active</div>';
        
        // Check if hooks are registered
        global $wp_filter;
        
        $hooks_to_check = [
            'woocommerce_after_shop_loop_item' => 'Wishlist button in product loops',
            'woocommerce_single_product_summary' => 'Wishlist button on single product',
            'wp_ajax_woo_custom_toggle_wishlist' => 'AJAX handler for logged users',
            'wp_ajax_nopriv_woo_custom_toggle_wishlist' => 'AJAX handler for guests'
        ];
        
        foreach ($hooks_to_check as $hook => $description) {
            if (isset($wp_filter[$hook])) {
                echo '<div class="success">✓ ' . $description . ' hook registered</div>';
            } else {
                echo '<div class="error">✗ ' . $description . ' hook not registered</div>';
            }
        }
        
    } else {
        echo '<div class="error">✗ WooCommerce is not active</div>';
    }
    echo '</div>';

    // Test 4: Assets
    echo '<div class="test">';
    echo '<h2>4. Assets</h2>';
    
    $css_file = WOO_CUSTOM_PLUGIN_DIR . 'assets/css/woo-custom.css';
    $js_file = WOO_CUSTOM_PLUGIN_DIR . 'assets/js/woo-custom.js';
    
    if (file_exists($css_file)) {
        echo '<div class="success">✓ CSS file exists (' . filesize($css_file) . ' bytes)</div>';
    } else {
        echo '<div class="error">✗ CSS file missing</div>';
    }
    
    if (file_exists($js_file)) {
        echo '<div class="success">✓ JavaScript file exists (' . filesize($js_file) . ' bytes)</div>';
    } else {
        echo '<div class="error">✗ JavaScript file missing</div>';
    }
    
    // Check if scripts are enqueued (on frontend)
    if (wp_script_is('woo-custom-script', 'registered')) {
        echo '<div class="success">✓ JavaScript is registered</div>';
    } else {
        echo '<div class="warning">? JavaScript registration status (check on frontend)</div>';
    }
    echo '</div>';

    // Test 5: AJAX Endpoint Test
    echo '<div class="test">';
    echo '<h2>5. AJAX Endpoint Test</h2>';
    
    $ajax_url = admin_url('admin-ajax.php');
    echo '<div class="info">AJAX URL: ' . $ajax_url . '</div>';
    
    // Test nonce generation
    $nonce = wp_create_nonce('woo_custom_nonce');
    echo '<div class="info">Generated nonce: ' . $nonce . '</div>';
    
    // Check if we can verify the nonce
    if (wp_verify_nonce($nonce, 'woo_custom_nonce')) {
        echo '<div class="success">✓ Nonce verification works</div>';
    } else {
        echo '<div class="error">✗ Nonce verification failed</div>';
    }
    echo '</div>';

    // Test 6: User and Product Test
    echo '<div class="test">';
    echo '<h2>6. User and Product Test</h2>';
    
    $current_user = wp_get_current_user();
    if ($current_user->ID) {
        echo '<div class="success">✓ User logged in (ID: ' . $current_user->ID . ')</div>';
        
        // Get a sample product for testing
        $products = wc_get_products(['limit' => 1, 'status' => 'publish']);
        if (!empty($products)) {
            $product = $products[0];
            echo '<div class="success">✓ Sample product found (ID: ' . $product->get_id() . ', Name: ' . $product->get_name() . ')</div>';
            
            // Test wishlist functionality
            if (class_exists('WooCustom_Wishlist')) {
                $wishlist = WooCustom_Wishlist::instance();
                
                // Test if product is in wishlist
                $is_in_wishlist = $wishlist->is_product_in_wishlist($product->get_id());
                echo '<div class="info">Product in wishlist: ' . ($is_in_wishlist ? 'Yes' : 'No') . '</div>';
                
                // Test add to wishlist
                if (!$is_in_wishlist) {
                    $add_result = $wishlist->add_to_wishlist($product->get_id());
                    if ($add_result) {
                        echo '<div class="success">✓ Successfully added product to wishlist</div>';
                        
                        // Test remove from wishlist
                        $remove_result = $wishlist->remove_from_wishlist($product->get_id());
                        if ($remove_result) {
                            echo '<div class="success">✓ Successfully removed product from wishlist</div>';
                        } else {
                            echo '<div class="error">✗ Failed to remove product from wishlist</div>';
                        }
                    } else {
                        echo '<div class="error">✗ Failed to add product to wishlist</div>';
                    }
                }
            }
        } else {
            echo '<div class="warning">? No products found for testing</div>';
        }
    } else {
        echo '<div class="warning">? No user logged in for testing</div>';
    }
    echo '</div>';

    // Test 7: JavaScript Console Test
    echo '<div class="test">';
    echo '<h2>7. JavaScript Test</h2>';
    echo '<div class="info">Check browser console for JavaScript errors when clicking wishlist buttons.</div>';
    echo '<div class="info">Test AJAX call manually:</div>';
    echo '<pre id="ajax-test-result">Click the button below to test AJAX functionality</pre>';
    echo '<button class="button" onclick="testAjax()">Test AJAX Call</button>';
    echo '</div>';
    ?>

    <script>
    function testAjax() {
        var resultDiv = document.getElementById('ajax-test-result');
        resultDiv.innerHTML = 'Testing AJAX...';
        
        // Test with a dummy product ID
        var data = {
            action: 'woo_custom_toggle_wishlist',
            product_id: 1,
            nonce: '<?php echo wp_create_nonce('woo_custom_nonce'); ?>'
        };
        
        var xhr = new XMLHttpRequest();
        xhr.open('POST', '<?php echo admin_url('admin-ajax.php'); ?>', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                resultDiv.innerHTML = 'Status: ' + xhr.status + '\nResponse: ' + xhr.responseText;
            }
        };
        
        var params = Object.keys(data).map(function(key) {
            return key + '=' + encodeURIComponent(data[key]);
        }).join('&');
        
        xhr.send(params);
    }
    </script>

    <div class="test">
        <h2>8. Recommendations</h2>
        <div class="info">
            <strong>If you're experiencing issues:</strong>
            <ul>
                <li>Check browser console for JavaScript errors</li>
                <li>Verify user is logged in when testing</li>
                <li>Clear browser cache and try again</li>
                <li>Check WordPress debug log for PHP errors</li>
                <li>Test with a default theme to rule out theme conflicts</li>
                <li>Deactivate other plugins to check for conflicts</li>
            </ul>
        </div>
    </div>

    <div class="test">
        <h2>9. Quick Actions</h2>
        <a href="<?php echo admin_url('plugins.php'); ?>" class="button">Manage Plugins</a>
        <a href="<?php echo admin_url('options-permalink.php'); ?>" class="button">Refresh Permalinks</a>
        <a href="<?php echo wc_get_page_permalink('shop'); ?>" class="button">Visit Shop</a>
        <a href="<?php echo wc_get_page_permalink('myaccount'); ?>" class="button">Visit My Account</a>
    </div>

</body>
</html>
