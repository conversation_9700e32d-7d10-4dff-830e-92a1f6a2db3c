<?php
/**
 * AJAX functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WooCustom_Ajax {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // AJAX actions for logged in users
        add_action('wp_ajax_woo_custom_toggle_wishlist', array($this, 'toggle_wishlist'));
        
        // AJAX actions for non-logged in users (redirect to login)
        add_action('wp_ajax_nopriv_woo_custom_toggle_wishlist', array($this, 'require_login'));
    }
    
    /**
     * Toggle product in wishlist
     */
    public function toggle_wishlist() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'woo_custom_nonce')) {
            wp_send_json_error(array(
                'message' => __('Security check failed', 'woo-custom')
            ));
        }
        
        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error(array(
                'message' => __('You must be logged in to use the wishlist', 'woo-custom'),
                'redirect' => wp_login_url()
            ));
        }
        
        // Get product ID
        $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
        if (!$product_id) {
            wp_send_json_error(array(
                'message' => __('Invalid product ID', 'woo-custom')
            ));
        }
        
        // Check if product exists
        $product = wc_get_product($product_id);
        if (!$product || !$product->exists()) {
            wp_send_json_error(array(
                'message' => __('Product not found', 'woo-custom')
            ));
        }
        
        $wishlist = WooCustom_Wishlist::instance();
        $user_id = get_current_user_id();
        
        // Check if product is in wishlist
        $is_in_wishlist = $wishlist->is_product_in_wishlist($product_id, $user_id);
        
        if ($is_in_wishlist) {
            // Remove from wishlist
            $result = $wishlist->remove_from_wishlist($product_id, $user_id);
            if ($result) {
                wp_send_json_success(array(
                    'action' => 'removed',
                    'message' => __('Removed from wishlist', 'woo-custom'),
                    'count' => $wishlist->get_wishlist_count($user_id)
                ));
            } else {
                wp_send_json_error(array(
                    'message' => __('Failed to remove from wishlist', 'woo-custom')
                ));
            }
        } else {
            // Add to wishlist
            $result = $wishlist->add_to_wishlist($product_id, $user_id);
            if ($result) {
                wp_send_json_success(array(
                    'action' => 'added',
                    'message' => __('Added to wishlist', 'woo-custom'),
                    'count' => $wishlist->get_wishlist_count($user_id)
                ));
            } else {
                wp_send_json_error(array(
                    'message' => __('Failed to add to wishlist', 'woo-custom')
                ));
            }
        }
    }
    
    /**
     * Require login for non-logged in users
     */
    public function require_login() {
        wp_send_json_error(array(
            'message' => __('You must be logged in to use the wishlist', 'woo-custom'),
            'redirect' => wp_login_url()
        ));
    }
}
