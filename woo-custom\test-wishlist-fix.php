<?php
/**
 * Test page for Wishlist functionality fixes
 * 
 * This page helps test the wishlist functionality after applying fixes.
 * Access via: yoursite.com/wp-content/plugins/woo-custom/test-wishlist-fix.php?test=1
 */

// Load WordPress
$wp_load_path = '../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('WordPress not found. Please check the path.');
}

// Only allow admin access
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

// Only run if test parameter is set
if (!isset($_GET['test'])) {
    die('Add ?test=1 to the URL to run this test.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Woo Custom Wishlist Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        .button { background: #007cba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px; margin: 5px; display: inline-block; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Woo Custom Wishlist Fix Test</h1>
    <p>Testing the fixes applied to resolve wishlist functionality issues.</p>
    <p>Generated on: <?php echo date('Y-m-d H:i:s'); ?></p>

    <?php
    // Test 1: Check if fixes are applied
    echo '<div class="test">';
    echo '<h2>1. Fix Verification</h2>';
    
    // Check if enhanced error handling is in place
    if (class_exists('WooCustom')) {
        $main_plugin = WooCustom::instance();
        if (method_exists($main_plugin, 'ensure_table_exists')) {
            echo '<div class="success">✓ Enhanced table creation method available</div>';
        } else {
            echo '<div class="error">✗ Enhanced table creation method not found</div>';
        }
    }
    
    // Check if AJAX class has enhanced error handling
    if (class_exists('WooCustom_Ajax')) {
        $ajax_class = new ReflectionClass('WooCustom_Ajax');
        $toggle_method = $ajax_class->getMethod('toggle_wishlist');
        $method_content = file_get_contents($ajax_class->getFileName());
        
        if (strpos($method_content, 'debug_mode') !== false) {
            echo '<div class="success">✓ Enhanced AJAX error handling detected</div>';
        } else {
            echo '<div class="error">✗ Enhanced AJAX error handling not found</div>';
        }
    }
    
    echo '</div>';

    // Test 2: Database Table Test
    echo '<div class="test">';
    echo '<h2>2. Database Table Test</h2>';
    
    if (class_exists('WooCustom')) {
        $main_plugin = WooCustom::instance();
        $table_exists = $main_plugin->ensure_table_exists();
        
        if ($table_exists) {
            echo '<div class="success">✓ Wishlist table exists and is accessible</div>';
            
            global $wpdb;
            $table_name = $wpdb->prefix . 'woo_custom_wishlist';
            
            // Test table structure
            $columns = $wpdb->get_results("DESCRIBE $table_name");
            $expected_columns = ['id', 'user_id', 'product_id', 'date_added'];
            $found_columns = array_column($columns, 'Field');
            
            $missing_columns = array_diff($expected_columns, $found_columns);
            if (empty($missing_columns)) {
                echo '<div class="success">✓ Table structure is correct</div>';
            } else {
                echo '<div class="error">✗ Missing columns: ' . implode(', ', $missing_columns) . '</div>';
            }
            
        } else {
            echo '<div class="error">✗ Wishlist table could not be created or accessed</div>';
        }
    }
    
    echo '</div>';

    // Test 3: AJAX Endpoint Test
    echo '<div class="test">';
    echo '<h2>3. AJAX Endpoint Test</h2>';
    
    $ajax_url = admin_url('admin-ajax.php');
    echo '<div class="info">AJAX URL: ' . $ajax_url . '</div>';
    
    // Test if AJAX actions are registered
    global $wp_filter;
    $ajax_actions = [
        'wp_ajax_woo_custom_toggle_wishlist' => 'Logged users',
        'wp_ajax_nopriv_woo_custom_toggle_wishlist' => 'Non-logged users'
    ];
    
    foreach ($ajax_actions as $action => $description) {
        if (isset($wp_filter[$action])) {
            echo '<div class="success">✓ ' . $description . ' AJAX action registered</div>';
        } else {
            echo '<div class="error">✗ ' . $description . ' AJAX action not registered</div>';
        }
    }
    
    echo '</div>';

    // Test 4: JavaScript Test
    echo '<div class="test">';
    echo '<h2>4. JavaScript Configuration Test</h2>';
    
    // Check if scripts are properly localized
    wp_enqueue_script('woo-custom-script');
    do_action('wp_enqueue_scripts');
    
    echo '<div class="info">Testing JavaScript configuration...</div>';
    echo '<div id="js-test-result">JavaScript test will run automatically</div>';
    
    echo '</div>';

    // Test 5: Live Functionality Test
    echo '<div class="test">';
    echo '<h2>5. Live Functionality Test</h2>';
    
    $current_user = wp_get_current_user();
    if ($current_user->ID) {
        echo '<div class="success">✓ User logged in (ID: ' . $current_user->ID . ')</div>';
        
        // Get a sample product
        $products = wc_get_products(['limit' => 1, 'status' => 'publish']);
        if (!empty($products)) {
            $product = $products[0];
            echo '<div class="success">✓ Test product found: ' . $product->get_name() . ' (ID: ' . $product->get_id() . ')</div>';
            
            echo '<div class="info">Test the wishlist functionality:</div>';
            echo '<div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0;">';
            echo '<h4>' . $product->get_name() . '</h4>';
            
            // Simulate wishlist button
            if (class_exists('WooCustom_Wishlist')) {
                $wishlist = WooCustom_Wishlist::instance();
                $is_in_wishlist = $wishlist->is_product_in_wishlist($product->get_id());
                $class = $is_in_wishlist ? 'woo-custom-wishlist-btn in-wishlist' : 'woo-custom-wishlist-btn';
                
                echo '<button class="' . $class . '" data-product-id="' . $product->get_id() . '" style="background: #f0f0f0; border: 1px solid #ccc; padding: 8px 12px; cursor: pointer;">';
                echo '<span class="heart-icon">♡</span>';
                echo '<span class="heart-icon-filled" style="display: ' . ($is_in_wishlist ? 'inline' : 'none') . ';">♥</span>';
                echo ' Test Wishlist Button';
                echo '</button>';
                
                echo '<div id="wishlist-test-result" style="margin-top: 10px; padding: 10px; background: #f9f9f9;"></div>';
            }
            echo '</div>';
            
        } else {
            echo '<div class="warning">? No products found for testing</div>';
        }
    } else {
        echo '<div class="warning">? Please log in to test wishlist functionality</div>';
        echo '<a href="' . wp_login_url(get_current_url()) . '" class="button">Login</a>';
    }
    
    echo '</div>';

    // Test 6: Error Log Check
    echo '<div class="test">';
    echo '<h2>6. Error Log Information</h2>';
    
    if (defined('WP_DEBUG') && WP_DEBUG) {
        echo '<div class="success">✓ WordPress debug mode is enabled</div>';
        echo '<div class="info">Check your WordPress error log for detailed debugging information</div>';
        
        if (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
            echo '<div class="success">✓ Debug logging is enabled</div>';
            $log_file = WP_CONTENT_DIR . '/debug.log';
            if (file_exists($log_file)) {
                echo '<div class="info">Debug log file: ' . $log_file . '</div>';
                echo '<div class="info">File size: ' . size_format(filesize($log_file)) . '</div>';
            }
        } else {
            echo '<div class="warning">? Debug logging is not enabled</div>';
        }
    } else {
        echo '<div class="warning">? WordPress debug mode is disabled</div>';
        echo '<div class="info">Enable WP_DEBUG in wp-config.php for detailed error information</div>';
    }
    
    echo '</div>';
    ?>

    <script>
    // Test JavaScript configuration
    document.addEventListener('DOMContentLoaded', function() {
        var resultDiv = document.getElementById('js-test-result');
        
        if (typeof woo_custom_ajax === 'undefined') {
            resultDiv.innerHTML = '<div style="color: red;">✗ woo_custom_ajax object not found</div>';
            return;
        }
        
        var tests = [];
        
        if (woo_custom_ajax.ajax_url) {
            tests.push('<div style="color: green;">✓ AJAX URL configured: ' + woo_custom_ajax.ajax_url + '</div>');
        } else {
            tests.push('<div style="color: red;">✗ AJAX URL not configured</div>');
        }
        
        if (woo_custom_ajax.nonce) {
            tests.push('<div style="color: green;">✓ Security nonce available</div>');
        } else {
            tests.push('<div style="color: red;">✗ Security nonce not available</div>');
        }
        
        if (woo_custom_ajax.i18n) {
            tests.push('<div style="color: green;">✓ Internationalization strings loaded</div>');
        } else {
            tests.push('<div style="color: orange;">? Internationalization strings not found</div>');
        }
        
        resultDiv.innerHTML = tests.join('');
        
        // Test wishlist button if available
        var wishlistBtn = document.querySelector('.woo-custom-wishlist-btn');
        if (wishlistBtn) {
            wishlistBtn.addEventListener('click', function(e) {
                e.preventDefault();
                var resultDiv = document.getElementById('wishlist-test-result');
                resultDiv.innerHTML = 'Testing wishlist functionality...';
                
                var productId = this.getAttribute('data-product-id');
                var data = new FormData();
                data.append('action', 'woo_custom_toggle_wishlist');
                data.append('product_id', productId);
                data.append('nonce', woo_custom_ajax.nonce);
                
                fetch(woo_custom_ajax.ajax_url, {
                    method: 'POST',
                    body: data
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.innerHTML = '<div style="color: green;">✓ Success: ' + (data.data.message || 'Wishlist updated') + '</div>';
                        // Update button state
                        if (data.data.action === 'added') {
                            this.classList.add('in-wishlist');
                            this.querySelector('.heart-icon').style.display = 'none';
                            this.querySelector('.heart-icon-filled').style.display = 'inline';
                        } else {
                            this.classList.remove('in-wishlist');
                            this.querySelector('.heart-icon').style.display = 'inline';
                            this.querySelector('.heart-icon-filled').style.display = 'none';
                        }
                    } else {
                        resultDiv.innerHTML = '<div style="color: red;">✗ Error: ' + (data.data.message || 'Unknown error') + '</div>';
                        if (data.data.debug) {
                            resultDiv.innerHTML += '<div style="color: orange;">Debug: ' + data.data.debug + '</div>';
                        }
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div style="color: red;">✗ Network Error: ' + error.message + '</div>';
                });
            });
        }
    });
    </script>

    <div class="test">
        <h2>7. Next Steps</h2>
        <div class="info">
            <strong>If tests pass:</strong>
            <ul>
                <li>Test the wishlist functionality on your actual shop pages</li>
                <li>Check browser console for any JavaScript errors</li>
                <li>Test with both logged-in and logged-out users</li>
                <li>Verify the wishlist page in My Account works correctly</li>
            </ul>
            
            <strong>If tests fail:</strong>
            <ul>
                <li>Check the WordPress error log for detailed error messages</li>
                <li>Ensure WooCommerce is active and working</li>
                <li>Try deactivating and reactivating the woo-custom plugin</li>
                <li>Clear any caching plugins</li>
                <li>Test with a default WordPress theme to rule out theme conflicts</li>
            </ul>
        </div>
    </div>

    <div class="test">
        <h2>8. Quick Actions</h2>
        <a href="<?php echo admin_url('plugins.php'); ?>" class="button">Manage Plugins</a>
        <a href="<?php echo wc_get_page_permalink('shop'); ?>" class="button">Visit Shop</a>
        <a href="<?php echo wc_get_page_permalink('myaccount'); ?>" class="button">Visit My Account</a>
        <a href="<?php echo admin_url('admin.php?page=wc-status&tab=logs'); ?>" class="button">View WooCommerce Logs</a>
    </div>

</body>
</html>

<?php
function get_current_url() {
    return (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
}
?>
