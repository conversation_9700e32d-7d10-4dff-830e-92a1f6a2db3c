<?php
/**
 * Wishlist functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WooCustom_Wishlist {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Add heart icon to product loops
        add_action('woocommerce_after_shop_loop_item', array($this, 'add_wishlist_button'), 15);
        add_action('woocommerce_single_product_summary', array($this, 'add_wishlist_button_single'), 35);
    }
    
    /**
     * Add wishlist button to product loops
     */
    public function add_wishlist_button() {
        global $product;

        if (!$product) {
            return;
        }

        $product_id = $product->get_id();
        $is_logged_in = is_user_logged_in();
        $is_in_wishlist = $is_logged_in ? $this->is_product_in_wishlist($product_id) : false;

        $class = 'woo-custom-wishlist-btn';
        if ($is_in_wishlist) {
            $class .= ' in-wishlist';
        }
        if (!$is_logged_in) {
            $class .= ' requires-login';
        }

        $title = $is_logged_in ?
            ($is_in_wishlist ? __('Remove from wishlist', 'woo-custom') : __('Add to wishlist', 'woo-custom')) :
            __('Login to add to wishlist', 'woo-custom');

        echo '<button class="' . esc_attr($class) . '" data-product-id="' . esc_attr($product_id) . '" title="' . esc_attr($title) . '">';
        echo '<span class="heart-icon">♡</span>';
        echo '<span class="heart-icon-filled">♥</span>';
        echo '</button>';
    }
    
    /**
     * Add wishlist button to single product page
     */
    public function add_wishlist_button_single() {
        global $product;

        if (!$product) {
            return;
        }

        $product_id = $product->get_id();
        $is_logged_in = is_user_logged_in();
        $is_in_wishlist = $is_logged_in ? $this->is_product_in_wishlist($product_id) : false;

        $class = 'woo-custom-wishlist-btn single-product';
        if ($is_in_wishlist) {
            $class .= ' in-wishlist';
        }
        if (!$is_logged_in) {
            $class .= ' requires-login';
        }

        $button_text = $is_logged_in ?
            ($is_in_wishlist ? __('Remove from Wishlist', 'woo-custom') : __('Add to Wishlist', 'woo-custom')) :
            __('Login to Add to Wishlist', 'woo-custom');

        echo '<div class="woo-custom-wishlist-wrapper">';
        echo '<button class="' . esc_attr($class) . '" data-product-id="' . esc_attr($product_id) . '">';
        echo '<span class="heart-icon">♡</span>';
        echo '<span class="heart-icon-filled">♥</span>';
        echo '<span class="wishlist-text">' . esc_html($button_text) . '</span>';
        echo '</button>';
        echo '</div>';
    }
    
    /**
     * Check if product is in user's wishlist
     */
    public function is_product_in_wishlist($product_id, $user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return false;
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'woo_custom_wishlist';
        
        $result = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE user_id = %d AND product_id = %d",
            $user_id,
            $product_id
        ));
        
        return !empty($result);
    }
    
    /**
     * Add product to wishlist
     */
    public function add_to_wishlist($product_id, $user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        if (!$user_id) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Woo Custom: add_to_wishlist called without valid user ID');
            }
            return false;
        }

        // Validate product ID
        if (!$product_id || !is_numeric($product_id)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Woo Custom: add_to_wishlist called with invalid product ID: ' . $product_id);
            }
            return false;
        }

        // Check if already in wishlist
        if ($this->is_product_in_wishlist($product_id, $user_id)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Woo Custom: Product ' . $product_id . ' already in wishlist for user ' . $user_id);
            }
            return false;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'woo_custom_wishlist';

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
        if (!$table_exists) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Woo Custom: Wishlist table does not exist: ' . $table_name);
            }
            return false;
        }

        $result = $wpdb->insert(
            $table_name,
            array(
                'user_id' => $user_id,
                'product_id' => $product_id,
                'date_added' => current_time('mysql')
            ),
            array('%d', '%d', '%s')
        );

        if ($result === false && defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Woo Custom: Database error in add_to_wishlist: ' . $wpdb->last_error);
        }

        return $result !== false;
    }
    
    /**
     * Remove product from wishlist
     */
    public function remove_from_wishlist($product_id, $user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        if (!$user_id) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Woo Custom: remove_from_wishlist called without valid user ID');
            }
            return false;
        }

        // Validate product ID
        if (!$product_id || !is_numeric($product_id)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Woo Custom: remove_from_wishlist called with invalid product ID: ' . $product_id);
            }
            return false;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'woo_custom_wishlist';

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
        if (!$table_exists) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Woo Custom: Wishlist table does not exist: ' . $table_name);
            }
            return false;
        }

        $result = $wpdb->delete(
            $table_name,
            array(
                'user_id' => $user_id,
                'product_id' => $product_id
            ),
            array('%d', '%d')
        );

        if ($result === false && defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Woo Custom: Database error in remove_from_wishlist: ' . $wpdb->last_error);
        }

        return $result !== false;
    }
    
    /**
     * Get user's wishlist products
     */
    public function get_wishlist_products($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return array();
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'woo_custom_wishlist';
        
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT product_id, date_added FROM $table_name WHERE user_id = %d ORDER BY date_added DESC",
            $user_id
        ));
        
        $products = array();
        foreach ($results as $result) {
            $product = wc_get_product($result->product_id);
            if ($product && $product->exists()) {
                $products[] = array(
                    'product' => $product,
                    'date_added' => $result->date_added
                );
            }
        }
        
        return $products;
    }
    
    /**
     * Get wishlist count for user
     */
    public function get_wishlist_count($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return 0;
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'woo_custom_wishlist';
        
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE user_id = %d",
            $user_id
        ));
        
        return intval($count);
    }
}
